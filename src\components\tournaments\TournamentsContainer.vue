<script setup lang="ts">
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { refDebounced, useWindowSize } from '@vueuse/core'
import {
  Search,
  Filter,
  Calendar as CalendarIcon,
  MapPin,
  Users,
} from 'lucide-vue-next'
import { computed, ref, onMounted } from 'vue'
import { Slider } from '@/components/ui/slider'
import { useAuthStore } from '@/stores/auth'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import TournamentsList from './TournamentsList.vue'
import TournamentDetailsWidget from './TournamentDetailsWidget.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

import ArcheryMap from '@/components/map/ArcheryMap.vue'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import type { Tournament, Match } from '@/api/feathers-client'

// Extended Tournament type with populated matches
interface TournamentWithMatches extends Tournament {
  matches?: Match[]
}
import { useTournamentsService } from '@/stores/tournaments'
import { useI18n } from 'vue-i18n'

interface TournamentsProps {
  defaultLayout?: number[]
  navCollapsedSize?: number
}

const props = withDefaults(defineProps<TournamentsProps>(), {
  defaultLayout: () => [265, 655],
  navCollapsedSize: 44
})

const { t } = useI18n()
const { width } = useWindowSize()
const tournamentsService = useTournamentsService()
const authStore = useAuthStore()
const userStore = useUserStore()
const { user } = storeToRefs(authStore)
const { activePlayer } = storeToRefs(userStore)

// Local state management
const tournaments = ref<TournamentWithMatches[]>([])
const currentTournament = ref<TournamentWithMatches | null>(null)
const hoveredTournaments = ref<TournamentWithMatches[]>([])
const hoveredTournamentId = ref<number | null>(null)
const isLoading = ref(false)
const error = ref<Error | null>(null)

// Computed matches for the selected tournament (from tournament.matches)
const tournamentMatches = computed(() => {
  if (!currentTournament.value?.matches) return []
  return currentTournament.value.matches
})

// Search and filter state
const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const currentTab = ref('all')

// Filter state
const showFilters = ref(true)
const filterThisWeek = ref(false)
const filterNextMonth = ref(false)
const filterNext3Months = ref(false)
const filterNext6Months = ref(false)
const filterThisYear = ref(false)
const filterNearMe = ref(false)
const filterAvailable = ref(false)
const nearMeDistance = ref([50]) // Distance in kilometers, default 50km

// Filter functions
const toggleFiltersVisibility = () => {
  showFilters.value = !showFilters.value
}

const setExclusiveTimeFilter = (filter: 'thisWeek' | 'nextMonth' | 'next3Months' | 'next6Months' | 'thisYear') => {
  filterThisWeek.value = filter === 'thisWeek'
  filterNextMonth.value = filter === 'nextMonth'
  filterNext3Months.value = filter === 'next3Months'
  filterNext6Months.value = filter === 'next6Months'
  filterThisYear.value = filter === 'thisYear'
}

const toggleThisWeekFilter = () => {
  if (!filterThisWeek.value) {
    setExclusiveTimeFilter('thisWeek')
  } else {
    filterThisWeek.value = false
  }
}

const toggleNextMonthFilter = () => {
  if (!filterNextMonth.value) {
    setExclusiveTimeFilter('nextMonth')
  } else {
    filterNextMonth.value = false
  }
}

const toggleNext3MonthsFilter = () => {
  if (!filterNext3Months.value) {
    setExclusiveTimeFilter('next3Months')
  } else {
    filterNext3Months.value = false
  }
}

const toggleNext6MonthsFilter = () => {
  if (!filterNext6Months.value) {
    setExclusiveTimeFilter('next6Months')
  } else {
    filterNext6Months.value = false
  }
}

const toggleThisYearFilter = () => {
  if (!filterThisYear.value) {
    setExclusiveTimeFilter('thisYear')
  } else {
    filterThisYear.value = false
  }
}

const toggleNearMeFilter = () => {
  filterNearMe.value = !filterNearMe.value
}

const toggleAvailableFilter = () => {
  filterAvailable.value = !filterAvailable.value
}

// Helper function for distance calculation (Haversine)
function deg2rad(deg: number) {
  return deg * (Math.PI / 180)
}

// Sidebar styles for responsive design
const sidebarStyles = computed(() => {
  if (width.value >= 1024) {
    return { '--sidebar-width': '25rem' }
  } else if (width.value >= 768) {
    return { '--sidebar-width': '15rem' }
  } else {
    return { '--sidebar-width': '25rem' }
  }
})

// Fetch tournaments
const fetchTournaments = async () => {
  isLoading.value = true
  error.value = null
  try {
    const result = await tournamentsService.findTournaments({
      query: {
        $limit: 100,
        isActive: true,
        completedAt: null,
        $populate: ['organizer', 'federation', 'matches']
      }
    })
    tournaments.value = result || []
  } catch (err) {
    error.value = err instanceof Error ? err : new Error('Failed to fetch tournaments')
    console.error('Failed to fetch tournaments:', err)
  } finally {
    isLoading.value = false
  }
}

// Search filtered tournaments
const searchFilteredTournaments = computed(() => {
  if (!debouncedSearchQuery.value) {
    return tournaments.value
  }

  const query = debouncedSearchQuery.value.toLowerCase()
  return tournaments.value.filter(tournament =>
    tournament.name?.toLowerCase().includes(query) ||
    tournament.description?.toLowerCase().includes(query) ||
    tournament.organizer?.name?.toLowerCase().includes(query)
  )
})

// Badge filtered tournaments (includes search + badge filters)
const filteredTournaments = computed(() => {
  let itemsToFilter = searchFilteredTournaments.value

  // Get all matches from tournaments for date filtering
  const getAllMatchesFromTournaments = (tournaments: TournamentWithMatches[]) => {
    const matches = []
    for (const tournament of tournaments) {
      if (tournament.matches) {
        for (const match of tournament.matches) {
          matches.push({ ...match, tournamentId: tournament.id })
        }
      }
    }
    return matches
  }

  // "This week" filter
  if (filterThisWeek.value) {
    const today = new Date()
    const currentDay = today.getDay() // 0 (Sun) - 6 (Sat)
    const firstDayOfWeek = new Date(today)
    firstDayOfWeek.setDate(today.getDate() - (currentDay === 0 ? 6 : currentDay - 1))
    firstDayOfWeek.setHours(0, 0, 0, 0)

    const lastDayOfWeek = new Date(firstDayOfWeek)
    lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6)
    lastDayOfWeek.setHours(23, 59, 59, 999)

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches) return false
      return tournament.matches.some(match => {
        if (!match.startDate) return false
        const matchStartDate = new Date(match.startDate)
        return matchStartDate >= firstDayOfWeek && matchStartDate <= lastDayOfWeek
      })
    })
  }

  // "Next month" filter
  if (filterNextMonth.value) {
    const today = new Date()
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)
    nextMonth.setHours(0, 0, 0, 0)

    const endOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 2, 0)
    endOfNextMonth.setHours(23, 59, 59, 999)

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches) return false
      return tournament.matches.some(match => {
        if (!match.startDate) return false
        const matchStartDate = new Date(match.startDate)
        return matchStartDate >= nextMonth && matchStartDate <= endOfNextMonth
      })
    })
  }

  // "Next 3 months" filter
  if (filterNext3Months.value) {
    const today3 = new Date()
    today3.setHours(0, 0, 0, 0)

    const next3Months = new Date(today3)
    next3Months.setMonth(today3.getMonth() + 3)
    next3Months.setHours(23, 59, 59, 999)

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches) return false
      return tournament.matches.some(match => {
        if (!match.startDate) return false
        const matchStartDate = new Date(match.startDate)
        return matchStartDate >= today3 && matchStartDate <= next3Months
      })
    })
  }

  // "Next 6 months" filter
  if (filterNext6Months.value) {
    const today6 = new Date()
    today6.setHours(0, 0, 0, 0)

    const next6Months = new Date(today6)
    next6Months.setMonth(today6.getMonth() + 6)
    next6Months.setHours(23, 59, 59, 999)

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches) return false
      return tournament.matches.some(match => {
        if (!match.startDate) return false
        const matchStartDate = new Date(match.startDate)
        return matchStartDate >= today6 && matchStartDate <= next6Months
      })
    })
  }

  // "This year" filter
  if (filterThisYear.value) {
    const today = new Date()
    const startOfYear = new Date(today.getFullYear(), 0, 1)
    startOfYear.setHours(0, 0, 0, 0)

    const endOfYear = new Date(today.getFullYear(), 11, 31)
    endOfYear.setHours(23, 59, 59, 999)

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches) return false
      return tournament.matches.some(match => {
        if (!match.startDate) return false
        const matchStartDate = new Date(match.startDate)
        return matchStartDate >= startOfYear && matchStartDate <= endOfYear
      })
    })
  }

  // "Near me" filter
  if (filterNearMe.value) {
    const playerLat = typeof activePlayer.value?.latitude === 'number' ? activePlayer.value.latitude : undefined
    const playerLng = typeof activePlayer.value?.longitude === 'number' ? activePlayer.value.longitude : undefined

    if (typeof playerLat === 'number' && typeof playerLng === 'number') {
      const nearRadiusKm = nearMeDistance.value[0]

      itemsToFilter = itemsToFilter.filter(tournament => {
        if (!tournament.matches) return false
        return tournament.matches.some(match => {
          if (typeof match.latitude !== 'number' || typeof match.longitude !== 'number') return false
          const R = 6371 // Radius of the Earth in km
          const dLat = deg2rad(match.latitude - playerLat)
          const dLon = deg2rad(match.longitude - playerLng)
          const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(deg2rad(playerLat)) *
            Math.cos(deg2rad(match.latitude)) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2)
          const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
          const distanceKm = R * c
          return distanceKm <= nearRadiusKm
        })
      })
    }
  }

  // "Available" filter - tournaments with matches that have open registration
  if (filterAvailable.value) {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    itemsToFilter = itemsToFilter.filter(tournament => {
      if (!tournament.matches) return false
      return tournament.matches.some(match => {
        if (!match.startDate) return false
        const matchStartDate = new Date(match.startDate)
        const isUpcoming = matchStartDate >= today

        let registrationOpen = true
        if (match.registrationEnds) {
          const matchRegEndDate = new Date(match.registrationEnds)
          registrationOpen = matchRegEndDate >= today
        }
        const notFinished = match.registrationFinished !== true

        return isUpcoming && registrationOpen && notFinished
      })
    })
  }

  return itemsToFilter
})

// Active tournaments
const activeTournaments = computed(() =>
  filteredTournaments.value.filter(t => t.isActive)
)

// Completed tournaments
const completedTournaments = computed(() => {
  const now = new Date()
  return filteredTournaments.value.filter(t =>
    t.completedAt && new Date(t.completedAt) < now
  )
})

// No longer needed - matches come from tournament.matches

// Tournament selection handlers
const selectTournament = (id: number) => {
  if (currentTournament.value && currentTournament.value.id === id) {
    currentTournament.value = null // Deselect if the same tournament is clicked again
  } else {
    const tournament = tournaments.value.find(t => t.id === id)
    currentTournament.value = tournament || null
  }
}

const setHoveredTournamentId = (id: number | null) => {
  hoveredTournamentId.value = id
  if (id) {
    const tournament = tournaments.value.find(t => t.id === id)
    hoveredTournaments.value = tournament ? [tournament] : []
  } else {
    hoveredTournaments.value = []
  }
}

const handleSelectTournamentFromMap = (matchId: string) => {
  const id = parseInt(matchId, 10)
  // Find the tournament that contains this match from currently filtered tournaments
  for (const tournament of currentTabTournaments.value) {
    if (tournament.matches?.some((match: Match) => match.id === id)) {
      selectTournament(tournament.id)
      break
    }
  }
}

// Map coordinates - use selected tournament's first match coordinates if available
const mapLatitude = computed(() => {
  if (currentTournament.value?.matches && currentTournament.value.matches.length > 0) {
    const firstMatch = currentTournament.value.matches[0]
    return firstMatch.latitude || null
  }
  return null
})

const mapLongitude = computed(() => {
  if (currentTournament.value?.matches && currentTournament.value.matches.length > 0) {
    const firstMatch = currentTournament.value.matches[0]
    return firstMatch.longitude || null
  }
  return null
})

// Get the currently displayed tournaments based on active tab
const currentTabTournaments = computed(() => {
  switch (currentTab.value) {
    case 'active':
      return activeTournaments.value
    case 'completed':
      return completedTournaments.value
    case 'all':
    default:
      return filteredTournaments.value
  }
})

// All matches from currently filtered tournaments for map display
const allMatches = computed(() => {
  const matches = []
  for (const tournament of currentTabTournaments.value) {
    if (tournament.matches) {
      matches.push(...tournament.matches)
    }
  }
  return matches
})

// Matches to highlight when hovering over a tournament
const hoveredMatches = computed(() => {
  if (hoveredTournamentId.value) {
    const tournament = currentTabTournaments.value.find(t => t.id === hoveredTournamentId.value)
    return tournament?.matches || []
  }
  return []
})

onMounted(() => {
  fetchTournaments()
})
</script>

<template>
  <SidebarProvider :style="sidebarStyles">
    <SidebarInset>
      <div class="flex flex-col gap-4">
        <div v-if="isLoading" class="text-center p-4">
          Loading tournaments...
        </div>
        <div v-else-if="error" class="text-center p-4 text-red-500">
          Error loading tournaments: {{ error?.message }}
        </div>
        <div v-else>
          <Tabs v-model="currentTab" default-value="all" class="h-full">
            <div class="flex items-center gap-2 px-4 py-2">
              <!-- Mobile sidebar trigger -->
              <SidebarTrigger class="md:hidden" />

              <TabsList class="">
                <TabsTrigger value="all" class="">
                  {{ t('navigation.tournaments') }} <Badge class="ml-2" variant="outline">{{ filteredTournaments.length }}</Badge>
                </TabsTrigger>
                <TabsTrigger value="active" class="text-zinc-600 dark:text-zinc-200">
                  {{ t('filters.active') }} <Badge class="ml-2" variant="outline">{{ activeTournaments.length }}</Badge>
                </TabsTrigger>
                <TabsTrigger value="completed" class="text-zinc-600 dark:text-zinc-200">
                  {{ t('filters.completed') }} <Badge class="ml-2" variant="outline">{{ completedTournaments.length }}</Badge>
                </TabsTrigger>
              </TabsList>
            </div>

            <!-- Search and filters -->
            <div class="px-4 py-2 border-b space-y-3">
              <div class="flex items-center gap-2">
                <div class="relative flex-1">
                  <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    v-model="searchQuery"
                    placeholder="Search tournaments..."
                    class="pl-8"
                  />
                </div>
                <Button variant="outline" size="sm" @click="toggleFiltersVisibility">
                  <Filter class="h-4 w-4" />
                </Button>
              </div>

              <!-- Filter badges -->
              <div v-if="showFilters" class="space-y-3">
                <!-- Time-based filters -->
                <div class="space-y-2">
                  <div class="flex items-center gap-1">
                    <CalendarIcon class="h-4 w-4 text-muted-foreground" />
                    <span class="text-sm font-medium text-muted-foreground">{{ t('filters.timeFilters') }}</span>
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      :class="{ 'bg-primary text-primary-foreground': filterThisWeek }"
                      @click="toggleThisWeekFilter"
                    >
                      {{ t('filters.thisWeek') }}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      :class="{ 'bg-primary text-primary-foreground': filterNextMonth }"
                      @click="toggleNextMonthFilter"
                    >
                      {{ t('filters.nextMonth') }}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      :class="{ 'bg-primary text-primary-foreground': filterNext3Months }"
                      @click="toggleNext3MonthsFilter"
                    >
                      {{ t('filters.next3Months') }}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      :class="{ 'bg-primary text-primary-foreground': filterNext6Months }"
                      @click="toggleNext6MonthsFilter"
                    >
                      {{ t('filters.next6Months') }}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      :class="{ 'bg-primary text-primary-foreground': filterThisYear }"
                      @click="toggleThisYearFilter"
                    >
                      {{ t('filters.thisYear') }}
                    </Button>
                  </div>
                </div>

                <!-- Location and availability filters -->
                <div class="space-y-2">
                  <div class="flex items-center gap-1">
                    <MapPin class="h-4 w-4 text-muted-foreground" />
                    <span class="text-sm font-medium text-muted-foreground">{{ t('filters.locationFilters') }}</span>
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <div class="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        :class="{ 'bg-primary text-primary-foreground': filterNearMe }"
                        @click="toggleNearMeFilter"
                      >
                        {{ t('filters.nearMe') }}
                      </Button>
                      <div v-if="filterNearMe" class="flex items-center gap-2">
                        <Slider
                          v-model="nearMeDistance"
                          :max="200"
                          :min="5"
                          :step="5"
                          class="w-20"
                        />
                        <span class="text-xs text-muted-foreground">{{ nearMeDistance[0] }}km</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Availability filters -->
                <div class="space-y-2">
                  <div class="flex items-center gap-1">
                    <Users class="h-4 w-4 text-muted-foreground" />
                    <span class="text-sm font-medium text-muted-foreground">{{ t('filters.availabilityFilters') }}</span>
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      :class="{ 'bg-primary text-primary-foreground': filterAvailable }"
                      @click="toggleAvailableFilter"
                    >
                      {{ t('filters.available') }}
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <TabsContent value="all" class="h-[calc(100%-10rem)] overflow-auto">
              <TournamentsList
                :items="filteredTournaments"
                :hovered-tournaments="hoveredTournaments"
                :current-tournament="currentTournament"
                @select-tournament="selectTournament"
                @hover-tournament="setHoveredTournamentId"
              />
            </TabsContent>
            <TabsContent value="active" class="h-[calc(100%-10rem)] overflow-auto">
              <TournamentsList
                :items="activeTournaments"
                :hovered-tournaments="hoveredTournaments"
                :current-tournament="currentTournament"
                @select-tournament="selectTournament"
                @hover-tournament="setHoveredTournamentId"
              />
            </TabsContent>
            <TabsContent value="completed" class="h-[calc(100%-10rem)] overflow-auto">
              <TournamentsList
                :items="completedTournaments"
                :hovered-tournaments="hoveredTournaments"
                :current-tournament="currentTournament"
                @select-tournament="selectTournament"
                @hover-tournament="setHoveredTournamentId"
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </SidebarInset>
    <Sidebar
      class="sticky top-0 h-svh border-l"
      collapsible="offcanvas"
    >
      <SidebarContent class="flex flex-col gap-0">
        <div class="mb-0 border-b border-sidebar-border pb-0">
          <TournamentDetailsWidget
            :tournament="currentTournament"
            :matches="tournamentMatches"
            @close="currentTournament = null"
          />
        </div>

        <div class="mb-0 border-b border-sidebar-border pb-0">
          <div class="h-52">
            <ArcheryMap
              :latitude="mapLatitude"
              :longitude="mapLongitude"
              :matches-to-display="allMatches"
              :hovered-matches="hoveredMatches"
              :hovered-match-id="null"
              @select-match="handleSelectTournamentFromMap"
            />
          </div>
        </div>
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
