<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import type { Tournament, Match } from '@/api/feathers-client'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Trophy,
  Calendar,
  Users,
  MapPin,
  Building,
  Info as InfoIcon,
  ExternalLink,
  Target,
  X as XIcon,
  Eye as EyeIcon
} from 'lucide-vue-next'

// Extended Tournament type with populated matches
interface TournamentWithMatches extends Tournament {
  matches?: Match[]
}

const props = defineProps<{
  tournament?: TournamentWithMatches | null
  matches?: Match[] // Keep for backward compatibility, but use tournament.matches if available
}>()

const emit = defineEmits<{
  close: []
}>()

const router = useRouter()
const { t, locale } = useI18n()

const formatDate = (dateString?: string) => {
  if (!dateString) return t('common.na')
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const goToTournamentDetails = () => {
  if (props.tournament?.id) {
    router.push({ name: 'tournament-details', params: { id: props.tournament.id.toString() } })
  }
}

const organizerName = computed(() => {
  return props.tournament?.organizer?.name || `Organizer ${props.tournament?.organizerId}` || t('common.na')
})

const federationName = computed(() => {
  return props.tournament?.federation?.name || t('common.na')
})

const statusBadge = computed(() => {
  if (!props.tournament) return null

  if (!props.tournament.completedAt) {
    return { text: t('tournament.ongoing'), variant: 'default' as const, class: 'bg-blue-100 text-blue-800' }
  } else {
    return { text: t('tournament.ended'), variant: 'secondary' as const, class: 'bg-gray-100 text-gray-800' }
  }
})

const roundsInfo = computed(() => {
  if (!props.tournament) return null

  const total = props.tournament.totalRounds || 0
  const min = props.tournament.minRounds || 0

  if (total === 0) return null

  return {
    total,
    min,
    optional: total - min
  }
})

// Get matches from tournament.matches or fallback to matches prop
const tournamentMatches = computed(() => {
  return props.tournament?.matches || props.matches || []
})

const upcomingMatches = computed(() => {
  const matches = tournamentMatches.value
  if (!matches) return []
  const now = new Date()
  return matches.filter(match =>
    match.startDate && new Date(match.startDate) > now
  ) // Show all upcoming matches
})

const pastMatches = computed(() => {
  const matches = tournamentMatches.value
  if (!matches) return []
  const now = new Date()
  return matches.filter(match =>
    match.endDate ? new Date(match.endDate) < now :
    match.startDate && new Date(match.startDate) < now
  ) // Show all past matches
})

const goToMatch = (matchId: number) => {
  router.push({ name: 'match-details', params: { id: matchId.toString() } })
}

const handleClose = () => {
  emit('close')
}

const goToOrganizer = () => {
  if (props.tournament?.organizerId) {
    router.push({ name: 'organizer-details', params: { id: props.tournament.organizerId.toString() } })
  }
}
</script>

<template>
  <div class="p-4">
    <div v-if="!tournament" class="text-center text-muted-foreground">
      <Trophy class="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p class="text-sm">{{ t('tournament.selectToView') }}</p>
    </div>

    <div v-else class="space-y-4">
      <!-- Header with action buttons -->
      <div class="flex items-center justify-between">
        <div class="flex-1 min-w-0">
          <h3 class="font-semibold text-base leading-tight truncate">{{ tournament.name }}</h3>
          <div class="flex items-center gap-2 text-xs text-muted-foreground mt-1">
            <Trophy class="h-3 w-3" />
            <span>ID: {{ tournament.id }}</span>
          </div>
        </div>
        <div class="flex items-center gap-1">
          <Badge v-if="statusBadge" :variant="statusBadge.variant" :class="statusBadge.class">
            {{ statusBadge.text }}
          </Badge>
          <Button variant="ghost" size="icon" class="w-7 h-7" @click="goToTournamentDetails">
            <EyeIcon class="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="icon" class="w-7 h-7" @click="handleClose">
            <XIcon class="w-4 h-4" />
          </Button>
        </div>
      </div>

      <Separator />

      <!-- Tournament Details -->
      <div class="space-y-3">
        <!-- Organizer -->
        <div class="flex items-center gap-2 text-sm">
          <Building class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.organizer') }}:</span>
            <span
              class="ml-1 font-medium cursor-pointer hover:text-primary hover:underline"
              @click="goToOrganizer"
            >
              {{ organizerName }}
            </span>
          </div>
        </div>

        <!-- Federation -->
        <div v-if="federationName !== 'N/A'" class="flex items-center gap-2 text-sm">
          <MapPin class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.federation') }}:</span>
            <span class="ml-1 font-medium">{{ federationName }}</span>
          </div>
        </div>

        <!-- Created Date -->
        <div class="flex items-center gap-2 text-sm">
          <Calendar class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.created') }}:</span>
            <span class="ml-1 font-medium">{{ formatDate(tournament.createdAt) }}</span>
          </div>
        </div>

        <!-- Completed Date -->
        <div v-if="tournament.completedAt" class="flex items-center gap-2 text-sm">
          <Calendar class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.completed') }}:</span>
            <span class="ml-1 font-medium">{{ formatDate(tournament.completedAt) }}</span>
          </div>
        </div>

        <!-- Rounds Information -->
        <div v-if="roundsInfo" class="flex items-center gap-2 text-sm">
          <Users class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.rounds') }}:</span>
            <span class="ml-1 font-medium">
              {{ roundsInfo.total }} total
              <span v-if="roundsInfo.min > 0" class="text-muted-foreground">
                ({{ roundsInfo.min }} required)
              </span>
            </span>
          </div>
        </div>
      </div>

      <!-- Description -->
      <div v-if="tournament.description" class="space-y-2">
        <Separator />
        <div class="space-y-1">
          <div class="flex items-center gap-2">
            <InfoIcon class="h-4 w-4 text-muted-foreground" />
            <span class="text-sm font-medium">{{ t('tournament.description') }}</span>
          </div>
          <p class="text-sm text-muted-foreground leading-relaxed pl-6">
            {{ tournament.description }}
          </p>
        </div>
      </div>

      <!-- Tournament Matches -->
      <div class="space-y-2">
        <Separator />
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <Target class="h-4 w-4 text-muted-foreground" />
            <span class="text-sm font-medium">{{ t('tournament.matches') }}</span>
          </div>

          <div v-if="!tournamentMatches || tournamentMatches.length === 0" class="text-xs text-muted-foreground pl-6">
            {{ t('tournament.noMatches') }}
          </div>

          <div v-else class="space-y-2">
            <!-- Upcoming Matches -->
            <div v-if="upcomingMatches.length > 0" class="space-y-1">
              <div class="text-xs font-medium text-muted-foreground pl-6">{{ t('tournament.upcomingMatches') }}</div>
              <div class="space-y-1 pl-6">
                <div
                  v-for="match in upcomingMatches"
                  :key="match.id"
                  class="flex items-center justify-between text-xs p-1 rounded hover:bg-accent/50 cursor-pointer"
                  @click="goToMatch(match.id)"
                >
                  <div class="min-w-0 flex-1">
                    <div class="font-medium truncate">{{ match.name }}</div>
                    <div class="text-muted-foreground">{{ formatDate(match.startDate) }}</div>
                  </div>
                  <ExternalLink class="h-3 w-3 text-muted-foreground flex-shrink-0" />
                </div>
              </div>
            </div>

            <!-- Past Matches -->
            <div v-if="pastMatches.length > 0" class="space-y-1">
              <div class="text-xs font-medium text-muted-foreground pl-6">{{ t('tournament.pastMatches') }}</div>
              <div class="space-y-1 pl-6">
                <div
                  v-for="match in pastMatches"
                  :key="match.id"
                  class="flex items-center justify-between text-xs p-1 rounded hover:bg-accent/50 cursor-pointer opacity-75"
                  @click="goToMatch(match.id)"
                >
                  <div class="min-w-0 flex-1">
                    <div class="font-medium truncate">{{ match.name }}</div>
                    <div class="text-muted-foreground">{{ formatDate(match.startDate) }}</div>
                  </div>
                  <ExternalLink class="h-3 w-3 text-muted-foreground flex-shrink-0" />
                </div>
              </div>
            </div>

            <!-- Show total count -->
            <div v-if="tournamentMatches && tournamentMatches.length > 0" class="text-xs text-muted-foreground pl-6">
              {{ t('tournament.totalMatches', { count: tournamentMatches.length }) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="space-y-2">
        <Separator />
        <Button
          variant="outline"
          size="sm"
          class="w-full"
          @click="goToTournamentDetails"
        >
          <ExternalLink class="h-4 w-4 mr-2" />
          {{ t('tournament.viewDetails') }}
        </Button>
      </div>
    </div>
  </div>
</template>
